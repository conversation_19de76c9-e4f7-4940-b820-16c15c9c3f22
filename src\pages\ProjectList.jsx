import React from "react";
import { Link } from "react-router-dom";
import AddProject from "./AddProject";
import { useSelector } from "react-redux";
import { getProjects } from "../store/projectSlice";

export default function ProjectList() {
  const projects = useSelector(getProjects);
  return (
    <>
      <div className="min-h-screen bg-gray-100 p-8">
        <h1 className="text-3xl font-bold mb-4">Project List</h1>
        <div className="mb-4">
          <button className="bg-purple-500 hover:bg-purple-600 text-white p-2 rounded">
            <Link to="/projects/add">Add New Project</Link>
          </button>
        </div>
        <div>
          <ul className="list-none p-0">
            {projects.map((project) => (
              <li key={project.id} className="p-2 mb-2 bg-white rounded shadow">
                <h2 className="text-xl font-semibold">{project.name}</h2>
                <p>{project.description}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-sm text-gray-500">
                    Created at: {project.createdAt}
                  </span>
                  <span className="text-sm text-purple-500 hover:underline cursor-pointer">
                    <Link to={`/todos?projectId=${project.id}`}>
                      View Details
                    </Link>
                  </span>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </>
  );
}
