import React from 'react'
import { Link, useLocation } from 'react-router-dom'

export default function Navbar() {
  const location = useLocation()

  const isActive = (path) => {
    if (path === '/' || path === '/programme') {
      return location.pathname === '/' || location.pathname === '/programme'
    }
    return location.pathname === path
  }

  return (
    <header className="bg-green-800 text-white border-b-4 border-green-900">
      <div className="max-w-6xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold">🍽️ GESTIONNAIRE REPAS FAMILLE</h1>
            <p className="text-green-200 text-sm">Organisez vos repas de la semaine</p>
          </div>
          <div className="text-green-200 text-sm">
            Version 1.0
          </div>
        </div>
        <nav className="mt-3">
          <div className="flex space-x-2">
            <Link
              to="/programme"
              className={`px-3 py-2 text-sm font-medium border ${
                isActive('/programme')
                  ? 'bg-green-600 border-green-500 text-white'
                  : 'bg-green-700 border-green-600 text-green-100 hover:bg-green-600'
              }`}
            >
              📅 PLANNING
            </Link>
            <Link
              to="/plats"
              className={`px-3 py-2 text-sm font-medium border ${
                isActive('/plats')
                  ? 'bg-green-600 border-green-500 text-white'
                  : 'bg-green-700 border-green-600 text-green-100 hover:bg-green-600'
              }`}
            >
              🍲 MES PLATS
            </Link>
            <Link
              to="/suggestion-ia"
              className={`px-3 py-2 text-sm font-medium border ${
                isActive('/suggestion-ia')
                  ? 'bg-green-600 border-green-500 text-white'
                  : 'bg-green-700 border-green-600 text-green-100 hover:bg-green-600'
              }`}
            >
              🤖 IDÉES IA
            </Link>
          </div>
        </nav>
      </div>
    </header>
  )
}
