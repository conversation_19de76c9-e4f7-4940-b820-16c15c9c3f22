import React from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { getPlats } from '../store/platSlice'
import { getPlanning, definirRepas, reinitialiserPlanning } from '../store/planningSlice'

const jours = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>redi', '<PERSON><PERSON>', '<PERSON>manche']
const typesRepas = ['Déjeuner', 'Dîner']

export default function ProgrammePage() {
  const dispatch = useDispatch()
  const plats = useSelector(getPlats)
  const planning = useSelector(getPlanning)

  const handleChangementRepas = (jour, typeRepas, platId) => {
    const platIdNumber = platId === '' ? null : parseInt(platId)
    dispatch(definirRepas({ jour, typeRepas, platId: platIdNumber }))
  }

  const reinitialiser = () => {
    if (window.confirm('Êtes-vous sûr de vouloir réinitialiser tout le planning ?')) {
      dispatch(reinitialiserPlanning())
    }
  }

  const getPlatNom = (platId) => {
    if (!platId) return ''
    const plat = plats.find(p => p.id === platId)
    return plat ? plat.nom : ''
  }

  const compterRepasPlannifies = () => {
    let count = 0
    jours.forEach(jour => {
      typesRepas.forEach(typeRepas => {
        if (planning[jour] && planning[jour][typeRepas]) {
          count++
        }
      })
    })
    return count
  }

  return (
    <div className="space-y-4">
      <div className="bg-white shadow-lg rounded-lg border-l-4 border-purple-600">
        <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-4 py-4 rounded-t-lg">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-xl font-bold flex items-center">
                📅 PLANNING DE LA SEMAINE
              </h1>
              <p className="text-purple-200 text-sm mt-1">Organisez vos repas pour toute la famille</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">{compterRepasPlannifies()}/14</div>
              <div className="text-purple-200 text-sm">repas planifiés</div>
            </div>
          </div>
        </div>
        
        <div className="p-4">
          <div className="mb-4 text-center">
            <button
              onClick={reinitialiser}
              className="bg-red-700 text-white px-4 py-2 border-2 border-red-800 font-bold"
            >
              RÉINITIALISER LE PLANNING
            </button>
          </div>

          {plats.length === 0 ? (
            <div className="text-center py-8 bg-yellow-50 border-2 border-yellow-400">
              <p className="text-yellow-800 font-bold">
                ATTENTION: Aucun plat disponible !
              </p>
              <p className="text-yellow-700 mt-2">
                Veuillez d'abord ajouter des plats dans la section "Gestion des Plats"
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-2 border-gray-400">
                <thead>
                  <tr className="bg-gray-200">
                    <th className="border border-gray-400 px-3 py-2 font-bold">JOUR</th>
                    {typesRepas.map(typeRepas => (
                      <th key={typeRepas} className="border border-gray-400 px-3 py-2 font-bold">
                        {typeRepas.toUpperCase()}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {jours.map(jour => (
                    <tr key={jour}>
                      <td className="border border-gray-400 px-3 py-2 font-bold bg-gray-100">
                        {jour}
                      </td>
                      {typesRepas.map(typeRepas => (
                        <td key={`${jour}-${typeRepas}`} className="border border-gray-400 px-3 py-2">
                          <select
                            value={planning[jour] && planning[jour][typeRepas] ? planning[jour][typeRepas] : ''}
                            onChange={(e) => handleChangementRepas(jour, typeRepas, e.target.value)}
                            className="w-full p-2 border-2 border-gray-300 text-sm"
                          >
                            <option value="">-- Choisir un plat --</option>
                            {plats.map(plat => (
                              <option key={plat.id} value={plat.id}>
                                {plat.nom}
                              </option>
                            ))}
                          </select>
                          {planning[jour] && planning[jour][typeRepas] && (
                            <div className="mt-1 text-xs text-gray-600 italic">
                              {getPlatNom(planning[jour][typeRepas])}
                            </div>
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <div className="bg-white border-2 border-gray-400">
        <div className="bg-gray-300 border-b-2 border-gray-400 px-4 py-2">
          <h2 className="text-lg font-bold">RÉSUMÉ DE LA SEMAINE</h2>
        </div>
        
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 border-2 border-blue-300 p-3">
              <div className="font-bold text-blue-800">Total repas</div>
              <div className="text-2xl font-bold text-blue-900">{compterRepasPlannifies()}</div>
            </div>
            <div className="bg-green-50 border-2 border-green-300 p-3">
              <div className="font-bold text-green-800">Plats disponibles</div>
              <div className="text-2xl font-bold text-green-900">{plats.length}</div>
            </div>
            <div className="bg-orange-50 border-2 border-orange-300 p-3">
              <div className="font-bold text-orange-800">Déjeuners</div>
              <div className="text-2xl font-bold text-orange-900">
                {jours.filter(jour => planning[jour] && planning[jour]['Déjeuner']).length}
              </div>
            </div>
            <div className="bg-purple-50 border-2 border-purple-300 p-3">
              <div className="font-bold text-purple-800">Dîners</div>
              <div className="text-2xl font-bold text-purple-900">
                {jours.filter(jour => planning[jour] && planning[jour]['Dîner']).length}
              </div>
            </div>
          </div>

          {compterRepasPlannifies() > 0 && (
            <div className="mt-4">
              <h3 className="font-bold mb-2">PLATS LES PLUS UTILISÉS:</h3>
              <div className="text-sm">
                {(() => {
                  const compteurPlats = {}
                  jours.forEach(jour => {
                    typesRepas.forEach(typeRepas => {
                      const platId = planning[jour] && planning[jour][typeRepas]
                      if (platId) {
                        const nomPlat = getPlatNom(platId)
                        compteurPlats[nomPlat] = (compteurPlats[nomPlat] || 0) + 1
                      }
                    })
                  })
                  
                  return Object.entries(compteurPlats)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 3)
                    .map(([nom, count]) => (
                      <span key={nom} className="inline-block bg-gray-200 px-2 py-1 mr-2 mb-1 border">
                        {nom} ({count}x)
                      </span>
                    ))
                })()}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
