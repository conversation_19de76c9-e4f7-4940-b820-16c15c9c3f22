import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(
    "AIzaSyD3gFI7eGKBejGYuvrZ1DFgj4y1MQndpkQ"
  );

export const getAiHelp = async (todo,project) => {
    let prompt = `Vous etes un assistant qui vous aide à accomplir des tâches. Vous devez vous concentrer sur la tâche suivante:
     ${todo.title} description: ${todo.description}. Fournissez un plan détaillé ou des suggestions pour accomplir cette tâche efficacement.`;
    if (project){
        prompt += ` La tâche fait partie du projet: ${project.name}, qui a les détails suivants: ${project.description}.`;
    }
    prompt += " Fournissez des conseils pratiques et des étapes concrètes pour réussir cette tâche.";
    try{
  const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
  const result = await model.generateContent(prompt);
  const response = await result.response;
  const text = await response.text();
  return text.trim();
}
catch(error){
  console.log("Something Went Wrong");
  return "Une erreur s'est produite lors de la génération de la réponse. Veuillez réessayer plus tard.";
}
    
};
