import { createSlice } from "@reduxjs/toolkit";

const jours = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'];
const repas = ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'];

const initialPlanning = {};
jours.forEach(jour => {
  initialPlanning[jour] = {};
  repas.forEach(repas_type => {
    initialPlanning[jour][repas_type] = null;
  });
});

const planningSlice = createSlice({
  name: "planning",
  initialState: {
    planning: initialPlanning,
    loading: false,
    error: null,
  },
  reducers: {
    definirRepas: (state, action) => {
      const { jour, typeRepas, platId } = action.payload;
      if (state.planning[jour]) {
        state.planning[jour][typeRepas] = platId;
      }
    },
    supprimerRepas: (state, action) => {
      const { jour, typeRepas } = action.payload;
      if (state.planning[jour]) {
        state.planning[jour][typeRepas] = null;
      }
    },
    reinitialiserPlanning: (state) => {
      jours.forEach(jour => {
        state.planning[jour] = {};
        repas.forEach(repas_type => {
          state.planning[jour][repas_type] = null;
        });
      });
    },
  },
});

export const getPlanning = (state) => state.planning.planning;
export const getRepas = (state, jour, typeRepas) => 
  state.planning.planning[jour] ? state.planning.planning[jour][typeRepas] : null;

export const { definirRepas, supprimerRepas, reinitialiserPlanning } = planningSlice.actions;
export default planningSlice.reducer;
