import React, { useState } from 'react'
import { useDispatch } from 'react-redux'
import { obtenirSuggestionPlat } from '../api/geminiApi'
import { ajouterPlat } from '../store/platSlice'

export default function SuggestionIAPage() {
  const dispatch = useDispatch()
  const [description, setDescription] = useState('')
  const [suggestion, setSuggestion] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')

  const afficherMessage = (msg) => {
    setMessage(msg)
    setTimeout(() => setMessage(''), 3000)
  }

  const obtenirSuggestion = async () => {
    if (!description.trim()) {
      setError('Veuillez décrire votre envie de plat')
      return
    }

    setLoading(true)
    setError('')
    setSuggestion(null)

    try {
      const resultat = await obtenirSuggestionPlat(description)
      
      if (resultat.success) {
        setSuggestion(resultat.data)
      } else {
        setError(resultat.error)
      }
    } catch (err) {
      setError('Erreur lors de la communication avec l\'IA')
    } finally {
      setLoading(false)
    }
  }

  const ajouterSuggestionAuxPlats = () => {
    if (suggestion) {
      dispatch(ajouterPlat({
        nom: suggestion.nom,
        description: suggestion.description
      }))
      afficherMessage('Plat ajouté à votre liste avec succès !')
      setSuggestion(null)
      setDescription('')
    }
  }

  const nouvelleRecherche = () => {
    setSuggestion(null)
    setError('')
    setDescription('')
  }

  return (
    <div className="space-y-4">
      <div className="bg-white shadow-lg rounded-lg border-l-4 border-indigo-600">
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-4 rounded-t-lg">
          <h1 className="text-xl font-bold flex items-center">
            SUGGESTIONS INTELLIGENTES DE PLATS
          </h1>
          <p className="text-indigo-200 text-sm mt-1">Laissez l'IA vous inspirer pour vos repas</p>
        </div>
        
        <div className="p-4">
          <div className="mb-6">
            <label className="block font-bold mb-2">
              DÉCRIVEZ VOTRE ENVIE DE PLAT:
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows="3"
              className="w-full p-3 border-2 border-gray-300"
              placeholder="Ex: un plat végétarien rapide, une recette à base de poulet, quelque chose d'exotique..."
              disabled={loading}
            />
          </div>

          <div className="text-center mb-6">
            <button
              onClick={obtenirSuggestion}
              disabled={loading || !description.trim()}
              className={`px-6 py-3 border-2 font-bold ${
                loading || !description.trim()
                  ? 'bg-gray-400 border-gray-500 text-gray-700 cursor-not-allowed'
                  : 'bg-purple-700 border-purple-800 text-white hover:bg-purple-800'
              }`}
            >
              {loading ? 'GÉNÉRATION EN COURS...' : 'OBTENIR UNE SUGGESTION'}
            </button>
          </div>

          {message && (
            <div className="bg-green-50 border-2 border-green-400 p-3 mb-4">
              <strong>SUCCÈS:</strong> {message}
            </div>
          )}

          {error && (
            <div className="bg-red-50 border-2 border-red-400 p-3 mb-4">
              <strong>ERREUR:</strong> {error}
            </div>
          )}

          {loading && (
            <div className="bg-blue-50 border-2 border-blue-400 p-4 text-center">
              <div className="font-bold text-blue-800 mb-2">GÉNÉRATION EN COURS...</div>
              <div className="text-blue-600">L'IA analyse votre demande et prépare une suggestion personnalisée.</div>
              <div className="mt-2">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-800"></div>
              </div>
            </div>
          )}

          {suggestion && (
            <div className="bg-white border-2 border-green-400">
              <div className="bg-green-200 border-b-2 border-green-400 px-4 py-2">
                <h2 className="text-lg font-bold text-green-800">SUGGESTION GÉNÉRÉE PAR L'IA</h2>
              </div>
              
              <div className="p-4">
                <table className="w-full border-2 border-gray-400 mb-4">
                  <tr>
                    <td className="border border-gray-400 px-3 py-2 font-bold bg-gray-100 w-1/4">
                      NOM DU PLAT
                    </td>
                    <td className="border border-gray-400 px-3 py-2">
                      <span className="font-bold text-lg">{suggestion.nom}</span>
                    </td>
                  </tr>
                  <tr>
                    <td className="border border-gray-400 px-3 py-2 font-bold bg-gray-100">
                      DESCRIPTION
                    </td>
                    <td className="border border-gray-400 px-3 py-2">
                      {suggestion.description}
                    </td>
                  </tr>
                  {suggestion.ingredients && (
                    <tr>
                      <td className="border border-gray-400 px-3 py-2 font-bold bg-gray-100">
                        INGRÉDIENTS
                      </td>
                      <td className="border border-gray-400 px-3 py-2">
                        <ul className="list-disc list-inside">
                          {suggestion.ingredients.map((ingredient, index) => (
                            <li key={index}>{ingredient}</li>
                          ))}
                        </ul>
                      </td>
                    </tr>
                  )}
                  {suggestion.tempsPreparation && (
                    <tr>
                      <td className="border border-gray-400 px-3 py-2 font-bold bg-gray-100">
                        TEMPS DE PRÉPARATION
                      </td>
                      <td className="border border-gray-400 px-3 py-2">
                        {suggestion.tempsPreparation}
                      </td>
                    </tr>
                  )}
                  {suggestion.difficulte && (
                    <tr>
                      <td className="border border-gray-400 px-3 py-2 font-bold bg-gray-100">
                        DIFFICULTÉ
                      </td>
                      <td className="border border-gray-400 px-3 py-2">
                        <span className={`px-2 py-1 border font-bold ${
                          suggestion.difficulte === 'Facile' ? 'bg-green-100 border-green-400 text-green-800' :
                          suggestion.difficulte === 'Moyen' ? 'bg-yellow-100 border-yellow-400 text-yellow-800' :
                          'bg-red-100 border-red-400 text-red-800'
                        }`}>
                          {suggestion.difficulte}
                        </span>
                      </td>
                    </tr>
                  )}
                </table>

                <div className="text-center space-x-4">
                  <button
                    onClick={ajouterSuggestionAuxPlats}
                    className="bg-green-700 text-white px-6 py-2 border-2 border-green-800 font-bold"
                  >
                    AJOUTER À MES PLATS
                  </button>
                  <button
                    onClick={nouvelleRecherche}
                    className="bg-blue-700 text-white px-6 py-2 border-2 border-blue-800 font-bold"
                  >
                    NOUVELLE RECHERCHE
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>


    </div>
  )
}
