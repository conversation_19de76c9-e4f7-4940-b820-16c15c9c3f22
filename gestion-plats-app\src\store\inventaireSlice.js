import { createSlice } from "@reduxjs/toolkit";

const inventaireSlice = createSlice({
  name: "inventaire",
  initialState: {
    ingredients: [
      { id: 1, nom_ingredient: "Riz", quantite_stock: 5000, unite: "g", seuil_alerte: 1000 },
      { id: 2, nom_ingredient: "Tomates", quantite_stock: 2000, unite: "g", seuil_alerte: 500 },
      { id: 3, nom_ingredient: "Oignons", quantite_stock: 1500, unite: "g", seuil_alerte: 300 },
      { id: 4, nom_ingredient: "Huile", quantite_stock: 2000, unite: "ml", seuil_alerte: 500 },
      { id: 5, nom_ingredient: "Sel", quantite_stock: 1000, unite: "g", seuil_alerte: 200 },
    ],
    alertes: [],
    loading: false,
    error: null,
  },
  reducers: {
    ajouterIngredient: (state, action) => {
      const nouvelIngredient = {
        id: Date.now(),
        nom_ingredient: action.payload.nom_ingredient,
        quantite_stock: action.payload.quantite_stock,
        unite: action.payload.unite,
        seuil_alerte: action.payload.seuil_alerte,
        createdAt: new Date().toLocaleString(),
      };
      state.ingredients.push(nouvelIngredient);
    },
    
    modifierIngredient: (state, action) => {
      const { id, ...updates } = action.payload;
      const ingredient = state.ingredients.find(ing => ing.id === id);
      if (ingredient) {
        Object.assign(ingredient, updates);
        ingredient.updatedAt = new Date().toLocaleString();
      }
    },
    
    supprimerIngredient: (state, action) => {
      state.ingredients = state.ingredients.filter(ing => ing.id !== action.payload);
    },
    
    decrementerStock: (state, action) => {
      const { nom_ingredient, quantite } = action.payload;
      const ingredient = state.ingredients.find(ing => 
        ing.nom_ingredient.toLowerCase() === nom_ingredient.toLowerCase()
      );
      
      if (ingredient && ingredient.quantite_stock >= quantite) {
        ingredient.quantite_stock -= quantite;
        ingredient.updatedAt = new Date().toLocaleString();
        
        if (ingredient.quantite_stock < ingredient.seuil_alerte) {
          const alerteExiste = state.alertes.find(alerte => 
            alerte.nom_ingredient === ingredient.nom_ingredient
          );
          
          if (!alerteExiste) {
            state.alertes.push({
              id: Date.now(),
              nom_ingredient: ingredient.nom_ingredient,
              quantite_restante: ingredient.quantite_stock,
              unite: ingredient.unite,
              seuil_alerte: ingredient.seuil_alerte,
              timestamp: new Date().toISOString(),
            });
          }
        }
      }
    },
    
    ajouterStock: (state, action) => {
      const { nom_ingredient, quantite } = action.payload;
      const ingredient = state.ingredients.find(ing => 
        ing.nom_ingredient.toLowerCase() === nom_ingredient.toLowerCase()
      );
      
      if (ingredient) {
        ingredient.quantite_stock += quantite;
        ingredient.updatedAt = new Date().toLocaleString();
        
        if (ingredient.quantite_stock >= ingredient.seuil_alerte) {
          state.alertes = state.alertes.filter(alerte => 
            alerte.nom_ingredient !== ingredient.nom_ingredient
          );
        }
      }
    },
    
    supprimerAlerte: (state, action) => {
      state.alertes = state.alertes.filter(alerte => alerte.id !== action.payload);
    },
  },
  
});

// Selectors
export const getIngredients = (state) => state.inventaire.ingredients;
export const getIngredientById = (state, id) => state.inventaire.ingredients.find(ing => ing.id === id);
export const getIngredientByNom = (state, nom) =>
  state.inventaire.ingredients.find(ing => ing.nom_ingredient.toLowerCase() === nom.toLowerCase());
export const getAlertes = (state) => state.inventaire.alertes;
export const getIngredientsEnAlerte = (state) =>
  state.inventaire.ingredients.filter(ing => ing.quantite_stock < ing.seuil_alerte);

export const { 
  ajouterIngredient, 
  modifierIngredient, 
  supprimerIngredient, 
  decrementerStock, 
  ajouterStock, 
  supprimerAlerte 
} = inventaireSlice.actions;

export default inventaireSlice.reducer;
