import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route, Link } from 'react-router-dom'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import store, { persistor } from './store/store'
import GestionPlats from './pages/GestionPlats'
import GestionInventaire from './pages/GestionInventaire'
import ListePlats from './pages/ListePlats'
import Alertes from './pages/Alertes'

function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={<div className="p-4">Chargement...</div>} persistor={persistor}>
        <Router>
          <div className="min-h-screen bg-gray-200">
            <header className="bg-gray-800 text-white border-b-4 border-gray-600">
              <div className="max-w-6xl mx-auto px-4 py-3">
                <h1 className="text-xl font-bold mb-3">SYSTEME DE GESTION - PLATS & INVENTAIRE</h1>
                <nav>
                  <table className="w-full">
                    <tr>
                      <td className="border border-gray-600 bg-gray-700 px-3 py-2 text-center">
                        <Link to="/plats" className="text-white no-underline font-medium">
                          [ NOUVEAU PLAT ]
                        </Link>
                      </td>
                      <td className="border border-gray-600 bg-gray-700 px-3 py-2 text-center">
                        <Link to="/liste-plats" className="text-white no-underline font-medium">
                          [ CONSULTER PLATS ]
                        </Link>
                      </td>
                      <td className="border border-gray-600 bg-gray-700 px-3 py-2 text-center">
                        <Link to="/inventaire" className="text-white no-underline font-medium">
                          [ INVENTAIRE ]
                        </Link>
                      </td>
                      <td className="border border-gray-600 bg-gray-700 px-3 py-2 text-center">
                        <Link to="/alertes" className="text-white no-underline font-medium">
                          [ ALERTES STOCK ]
                        </Link>
                      </td>
                    </tr>
                  </table>
                </nav>
              </div>
            </header>

            <main className="max-w-6xl mx-auto p-4">
              <Routes>
                <Route path="/" element={<GestionPlats />} />
                <Route path="/plats" element={<GestionPlats />} />
                <Route path="/liste-plats" element={<ListePlats />} />
                <Route path="/inventaire" element={<GestionInventaire />} />
                <Route path="/alertes" element={<Alertes />} />
              </Routes>
            </main>
          </div>
        </Router>
      </PersistGate>
    </Provider>
  )
}

export default App
