import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import store, { persistor } from './store/store'
import Navbar from './components/Navbar'
import ProgrammePage from './pages/ProgrammePage'
import PlatsPage from './pages/PlatsPage'
import SuggestionIAPage from './pages/SuggestionIAPage'

function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={<div className="p-4">Chargement...</div>} persistor={persistor}>
        <Router>
          <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
            <Navbar />
            <main className="max-w-6xl mx-auto p-4">
              <Routes>
                <Route path="/" element={<ProgrammePage />} />
                <Route path="/programme" element={<ProgrammePage />} />
                <Route path="/plats" element={<PlatsPage />} />
                <Route path="/suggestion-ia" element={<SuggestionIAPage />} />
              </Routes>
            </main>
          </div>
        </Router>
      </PersistGate>
    </Provider>
  )
}

export default App
