import { combineReducers, configureStore } from "@reduxjs/toolkit";
import projectSlice from "./projectSlice";
import todoSlice from "./todoSlice";
import storage from "redux-persist/es/storage";
import { persistReducer, persistStore } from "redux-persist";

const persistConfig = {
  key: 'root',
  storage,
}
const rootReducer = combineReducers({
  project: projectSlice,
  todo: todoSlice,
})
const persistedReducer = persistReducer(persistConfig, rootReducer)
const store = configureStore({
  reducer: persistedReducer,
});


export default store;
export const persistor = persistStore(store);
