{"version": 3, "sources": ["../../redux-persist/es/storage/getStorage.js", "../../redux-persist/es/storage/createWebStorage.js", "../../redux-persist/es/storage/index.js"], "sourcesContent": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction noop() {}\n\nvar noopStorage = {\n  getItem: noop,\n  setItem: noop,\n  removeItem: noop\n};\n\nfunction hasStorage(storageType) {\n  if ((typeof self === \"undefined\" ? \"undefined\" : _typeof(self)) !== 'object' || !(storageType in self)) {\n    return false;\n  }\n\n  try {\n    var storage = self[storageType];\n    var testKey = \"redux-persist \".concat(storageType, \" test\");\n    storage.setItem(testKey, 'test');\n    storage.getItem(testKey);\n    storage.removeItem(testKey);\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') console.warn(\"redux-persist \".concat(storageType, \" test failed, persistence will be disabled.\"));\n    return false;\n  }\n\n  return true;\n}\n\nexport default function getStorage(type) {\n  var storageType = \"\".concat(type, \"Storage\");\n  if (hasStorage(storageType)) return self[storageType];else {\n    if (process.env.NODE_ENV !== 'production') {\n      console.error(\"redux-persist failed to create sync storage. falling back to noop storage.\");\n    }\n\n    return noopStorage;\n  }\n}", "import getStorage from './getStorage';\nexport default function createWebStorage(type) {\n  var storage = getStorage(type);\n  return {\n    getItem: function getItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.getItem(key));\n      });\n    },\n    setItem: function setItem(key, item) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.setItem(key, item));\n      });\n    },\n    removeItem: function removeItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.removeItem(key));\n      });\n    }\n  };\n}", "import createWebStorage from './createWebStorage';\nexport default createWebStorage('local');"], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK;AAAE,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,cAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,cAAU,SAASD,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAO,QAAQ,GAAG;AAAG;AAE9V,SAAS,OAAO;AAAC;AAEjB,IAAI,cAAc;AAAA,EAChB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AACd;AAEA,SAAS,WAAW,aAAa;AAC/B,OAAK,OAAO,SAAS,cAAc,cAAc,QAAQ,IAAI,OAAO,YAAY,EAAE,eAAe,OAAO;AACtG,WAAO;AAAA,EACT;AAEA,MAAI;AACF,QAAI,UAAU,KAAK,WAAW;AAC9B,QAAI,UAAU,iBAAiB,OAAO,aAAa,OAAO;AAC1D,YAAQ,QAAQ,SAAS,MAAM;AAC/B,YAAQ,QAAQ,OAAO;AACvB,YAAQ,WAAW,OAAO;AAAA,EAC5B,SAAS,GAAG;AACV,QAAI;AAAuC,cAAQ,KAAK,iBAAiB,OAAO,aAAa,6CAA6C,CAAC;AAC3I,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEe,SAAR,WAA4B,MAAM;AACvC,MAAI,cAAc,GAAG,OAAO,MAAM,SAAS;AAC3C,MAAI,WAAW,WAAW;AAAG,WAAO,KAAK,WAAW;AAAA,OAAO;AACzD,QAAI,MAAuC;AACzC,cAAQ,MAAM,4EAA4E;AAAA,IAC5F;AAEA,WAAO;AAAA,EACT;AACF;;;ACrCe,SAAR,iBAAkC,MAAM;AAC7C,MAAI,UAAU,WAAW,IAAI;AAC7B,SAAO;AAAA,IACL,SAAS,SAAS,QAAQ,KAAK;AAC7B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,gBAAQ,QAAQ,QAAQ,GAAG,CAAC;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA,IACA,SAAS,SAAS,QAAQ,KAAK,MAAM;AACnC,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,gBAAQ,QAAQ,QAAQ,KAAK,IAAI,CAAC;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAS,WAAW,KAAK;AACnC,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,gBAAQ,QAAQ,WAAW,GAAG,CAAC;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;ACnBA,IAAO,kBAAQ,iBAAiB,OAAO;", "names": ["_typeof", "obj"]}