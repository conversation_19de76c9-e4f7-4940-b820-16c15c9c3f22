{"indexes": [{"collectionGroup": "ingredients", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "ingredients", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "pantry", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "ingredientName", "order": "ASCENDING"}]}, {"collectionGroup": "pantry", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "expiryDate", "order": "ASCENDING"}]}, {"collectionGroup": "pantry", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "purchaseDate", "order": "DESCENDING"}]}, {"collectionGroup": "blogs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "authorId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "blogs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "visibility", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "blogs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "authorId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "blogs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "blogs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "visibility", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}