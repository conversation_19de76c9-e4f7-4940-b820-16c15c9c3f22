import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { ajouterPlat, supprimerPlat, modifierPlat, getPlats } from '../store/platSlice'

const schema = yup.object({
  nom: yup.string().min(3, 'Le nom doit faire au moins 3 caractères').required('Le nom est obligatoire'),
  description: yup.string().min(10, 'La description doit faire au moins 10 caractères').required('La description est obligatoire'),
}).required()

export default function PlatsPage() {
  const dispatch = useDispatch()
  const plats = useSelector(getPlats)
  const [platEnEdition, setPlatEnEdition] = useState(null)
  const [message, setMessage] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm({
    resolver: yupResolver(schema),
  })

  const afficherMessage = (msg) => {
    setMessage(msg)
    setTimeout(() => setMessage(''), 3000)
  }

  const soumettreFormulaire = (data) => {
    if (platEnEdition) {
      dispatch(modifierPlat({ id: platEnEdition.id, ...data }))
      afficherMessage('Plat modifié avec succès')
      setPlatEnEdition(null)
    } else {
      dispatch(ajouterPlat(data))
      afficherMessage('Plat ajouté avec succès')
    }
    reset()
  }

  const commencerEdition = (plat) => {
    setPlatEnEdition(plat)
    setValue('nom', plat.nom)
    setValue('description', plat.description)
  }

  const annulerEdition = () => {
    setPlatEnEdition(null)
    reset()
  }

  const supprimerPlatHandler = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce plat ?')) {
      dispatch(supprimerPlat(id))
      afficherMessage('Plat supprimé')
    }
  }

  return (
    <div className="space-y-6">
      <div className="bg-white border-2 border-gray-400">
        <div className="bg-gray-300 border-b-2 border-gray-400 px-4 py-2">
          <h2 className="text-lg font-bold">
            {platEnEdition ? 'MODIFIER LE PLAT' : 'AJOUTER UN NOUVEAU PLAT'}
          </h2>
        </div>
        
        <div className="p-4">
          {message && (
            <div className="bg-green-50 border-2 border-green-400 p-3 mb-4">
              <strong>SUCCÈS:</strong> {message}
            </div>
          )}

          <form onSubmit={handleSubmit(soumettreFormulaire)}>
            <table className="w-full border-2 border-gray-400 mb-4">
              <tr className="bg-gray-100">
                <td className="border border-gray-400 px-3 py-2 font-bold w-1/4">CHAMP</td>
                <td className="border border-gray-400 px-3 py-2 font-bold">VALEUR</td>
              </tr>
              <tr>
                <td className="border border-gray-400 px-3 py-2 font-medium">Nom du plat *</td>
                <td className="border border-gray-400 px-3 py-2">
                  <input
                    {...register("nom")}
                    type="text"
                    className={`w-full p-2 border-2 ${errors.nom ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="Ex: Spaghetti Bolognaise"
                  />
                  {errors.nom && (
                    <div className="text-red-600 text-sm mt-1 font-bold">
                      ERREUR: {errors.nom.message}
                    </div>
                  )}
                </td>
              </tr>
              <tr>
                <td className="border border-gray-400 px-3 py-2 font-medium">Description *</td>
                <td className="border border-gray-400 px-3 py-2">
                  <textarea
                    {...register("description")}
                    rows="3"
                    className={`w-full p-2 border-2 ${errors.description ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="Décrivez le plat en détail..."
                  />
                  {errors.description && (
                    <div className="text-red-600 text-sm mt-1 font-bold">
                      ERREUR: {errors.description.message}
                    </div>
                  )}
                </td>
              </tr>
            </table>

            <div className="text-center space-x-4">
              {platEnEdition && (
                <button
                  type="button"
                  onClick={annulerEdition}
                  className="bg-gray-600 text-white px-6 py-2 border-2 border-gray-700 font-bold"
                >
                  ANNULER
                </button>
              )}
              <button
                type="submit"
                className="bg-blue-800 text-white px-6 py-2 border-2 border-blue-900 font-bold"
              >
                {platEnEdition ? 'MODIFIER' : 'AJOUTER'}
              </button>
            </div>
          </form>
        </div>
      </div>

      <div className="bg-white border-2 border-gray-400">
        <div className="bg-gray-300 border-b-2 border-gray-400 px-4 py-2">
          <h2 className="text-lg font-bold">LISTE DES PLATS ({plats.length})</h2>
        </div>
        
        <div className="p-4">
          {plats.length === 0 ? (
            <div className="text-center py-8 text-gray-600">
              Aucun plat enregistré. Ajoutez votre premier plat ci-dessus.
            </div>
          ) : (
            <table className="w-full border-2 border-gray-400">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-400 px-3 py-2 font-bold text-left">NOM</th>
                  <th className="border border-gray-400 px-3 py-2 font-bold text-left">DESCRIPTION</th>
                  <th className="border border-gray-400 px-3 py-2 font-bold text-center">ACTIONS</th>
                </tr>
              </thead>
              <tbody>
                {plats.map(plat => (
                  <tr key={plat.id}>
                    <td className="border border-gray-400 px-3 py-2 font-medium">{plat.nom}</td>
                    <td className="border border-gray-400 px-3 py-2">{plat.description}</td>
                    <td className="border border-gray-400 px-3 py-2 text-center space-x-2">
                      <button
                        onClick={() => commencerEdition(plat)}
                        className="bg-orange-600 text-white px-3 py-1 border border-orange-700 text-sm font-bold"
                      >
                        MODIFIER
                      </button>
                      <button
                        onClick={() => supprimerPlatHandler(plat.id)}
                        className="bg-red-600 text-white px-3 py-1 border border-red-700 text-sm font-bold"
                      >
                        SUPPRIMER
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  )
}
