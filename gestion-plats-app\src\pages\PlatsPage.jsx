import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { ajouterPlat, supprimerPlat, modifierPlat, getPlats } from '../store/platSlice'

const schema = yup.object({
  nom: yup.string().min(3, 'Le nom doit faire au moins 3 caractères').required('Le nom est obligatoire'),
  description: yup.string().min(10, 'La description doit faire au moins 10 caractères').required('La description est obligatoire'),
}).required()

export default function PlatsPage() {
  const dispatch = useDispatch()
  const plats = useSelector(getPlats)
  const [platEnEdition, setPlatEnEdition] = useState(null)
  const [message, setMessage] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm({
    resolver: yupResolver(schema),
  })

  const afficherMessage = (msg) => {
    setMessage(msg)
    setTimeout(() => setMessage(''), 3000)
  }

  const soumettreFormulaire = (data) => {
    if (platEnEdition) {
      dispatch(modifierPlat({ id: platEnEdition.id, ...data }))
      afficherMessage('Plat modifié avec succès')
      setPlatEnEdition(null)
    } else {
      dispatch(ajouterPlat(data))
      afficherMessage('Plat ajouté avec succès')
    }
    reset()
  }

  const commencerEdition = (plat) => {
    setPlatEnEdition(plat)
    setValue('nom', plat.nom)
    setValue('description', plat.description)
  }

  const annulerEdition = () => {
    setPlatEnEdition(null)
    reset()
  }

  const supprimerPlatHandler = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce plat ?')) {
      dispatch(supprimerPlat(id))
      afficherMessage('Plat supprimé')
    }
  }

  return (
    <div className="space-y-4">
      <div className="bg-white shadow-lg rounded-lg border-l-4 border-green-600">
        <div className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-3 rounded-t-lg">
          <h2 className="text-lg font-bold flex items-center">
            {platEnEdition ? '✏️ MODIFIER LE PLAT' : '➕ AJOUTER UN NOUVEAU PLAT'}
          </h2>
        </div>
        
        <div className="p-6">
          {message && (
            <div className="bg-green-100 border-l-4 border-green-500 p-4 mb-4 rounded">
              <div className="flex items-center">
                <span className="text-green-600 mr-2">✅</span>
                <strong className="text-green-800">{message}</strong>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit(soumettreFormulaire)} className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg border">
                <label className="block text-gray-700 font-semibold mb-2">
                  📝 Nom du plat *
                </label>
                <input
                  {...register("nom")}
                  type="text"
                  className={`w-full p-3 border-2 rounded-lg ${errors.nom ? 'border-red-400 bg-red-50' : 'border-gray-300'} focus:border-green-500 focus:outline-none`}
                  placeholder="Ex: Spaghetti Bolognaise"
                />
                {errors.nom && (
                  <div className="text-red-600 text-sm mt-2 flex items-center">
                    <span className="mr-1">⚠️</span>
                    {errors.nom.message}
                  </div>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg border">
                <label className="block text-gray-700 font-semibold mb-2">
                  📄 Description du plat *
                </label>
                <textarea
                  {...register("description")}
                  rows="4"
                  className={`w-full p-3 border-2 rounded-lg ${errors.description ? 'border-red-400 bg-red-50' : 'border-gray-300'} focus:border-green-500 focus:outline-none`}
                  placeholder="Décrivez le plat en détail, ses ingrédients principaux, son goût..."
                />
                {errors.description && (
                  <div className="text-red-600 text-sm mt-2 flex items-center">
                    <span className="mr-1">⚠️</span>
                    {errors.description.message}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              {platEnEdition && (
                <button
                  type="button"
                  onClick={annulerEdition}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  ❌ Annuler
                </button>
              )}
              <button
                type="submit"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors shadow-lg"
              >
                {platEnEdition ? '✏️ Modifier' : '➕ Ajouter le plat'}
              </button>
            </div>
          </form>
        </div>
      </div>

      <div className="bg-white shadow-lg rounded-lg border-l-4 border-blue-600">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-t-lg">
          <h2 className="text-lg font-bold flex items-center">
            📋 MES PLATS ENREGISTRÉS ({plats.length})
          </h2>
        </div>

        <div className="p-6">
          {plats.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🍽️</div>
              <p className="text-gray-600 text-lg">Aucun plat enregistré</p>
              <p className="text-gray-500 text-sm mt-2">Ajoutez votre premier plat ci-dessus pour commencer !</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {plats.map(plat => (
                <div key={plat.id} className="bg-gray-50 rounded-lg p-4 border-l-4 border-green-500 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                      🍲 {plat.nom}
                    </h3>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => commencerEdition(plat)}
                        className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                        title="Modifier ce plat"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => supprimerPlatHandler(plat.id)}
                        className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                        title="Supprimer ce plat"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                  <p className="text-gray-700 text-sm leading-relaxed">{plat.description}</p>
                  {plat.createdAt && (
                    <p className="text-gray-500 text-xs mt-3">
                      📅 Ajouté le {plat.createdAt}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
