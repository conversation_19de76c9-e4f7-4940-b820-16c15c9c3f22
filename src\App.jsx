
import React from 'react'
import { useState } from 'react'
import TodoList from './pages/TodoList'
import { Link, Route, Routes } from 'react-router-dom'
import ProjectList from './pages/ProjectList'
import AddProject from './pages/AddProject'
import TodoDetails from './pages/TodoDetails'
import ProjectDetails from './pages/ProjectDetails'


function App() {

  return (
    <>
    <nav className="bg-purple-600 p-4 text-white">
      <ul className="flex space-x-4">
        <li><Link to="/todos">Todos</Link></li>
        <li><Link to="/projects">Projects</Link></li>
      </ul>
    </nav>
      <Routes>
        <Route path="/" element={<TodoList />} />
        <Route path="/todos" element={<TodoList />} />
        <Route path="/todos/:todoId" element={<TodoDetails />} />
        <Route path="/projects" element={<ProjectList />} />
        <Route path="/projects/add" element={<AddProject />} />
        <Route path="/projects/:projectId" element={<ProjectDetails />} />
        {/* Add more routes as needed */}
      </Routes>

        
      
 
    </>
  )
}

export default App
