{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "deploy:indexes": "node scripts/deploy-indexes.js", "setup:firestore": "node scripts/deploy-indexes.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/js-api-loader": "^1.16.8", "@googlemaps/react-wrapper": "^1.2.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.5.2", "@mui/x-date-pickers-pro": "^8.5.0", "date-fns": "^4.1.0", "firebase": "^11.8.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}