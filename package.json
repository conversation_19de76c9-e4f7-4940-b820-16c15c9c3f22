{"name": "demo_todo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "redux-persist": "^6.0.0", "tailwindcss": "^4.1.8", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}