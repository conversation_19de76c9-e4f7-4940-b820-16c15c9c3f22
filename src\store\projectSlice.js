import { createSlice } from "@reduxjs/toolkit";

const projectSlice = createSlice({
  name: "project",
  initialState: {
    projects: [
        { id: 1, name: 'Project Alpha', description: 'First project', createdAt: (new Date()).toLocaleString() },
        { id: 2, name: 'Project Beta', description: 'Second project', createdAt: (new Date()).toLocaleString() },
        { id: 3, name: 'Project Gamma', description: 'Third project', createdAt: (new Date()).toLocaleString() }
    ],
    loading: false,
    error: null,        
  },
  reducers: {
    addProject: (state, action) => {
      state.projects.push({
        id: state.projects.length + 1,
        ...action.payload,
        createdAt: (new Date()).toLocaleString(),
      });   
    },
    
 
  },
  selectors: {
    getProjects: (state) => state.projects,
    getProjectById: (state, id) => state.projects.find(project => project.id === id),
  },
});

export const { getProjects, getProjectById } = projectSlice.selectors;
export const { addProject } = projectSlice.actions;  
export default projectSlice.reducer;