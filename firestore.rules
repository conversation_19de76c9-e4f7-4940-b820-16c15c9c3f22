rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for authentication and authorization
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    function isFamilyMember(familyId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/families/$(familyId)/members/$(request.auth.uid));
    }

    function isFamilyAdmin(familyId) {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/families/$(familyId)/members/$(request.auth.uid)).data.role == 'admin';
    }

    // ===========================================
    // BLOG ARTICLES
    // ===========================================
    match /blogs/{blogId} {
      // Allow read for published public blogs (anyone can read)
      allow read: if resource.data.status == 'published' &&
                     resource.data.visibility == 'public';

      // Allow read for authenticated users on premium content
      allow read: if isAuthenticated() &&
                     resource.data.status == 'published' &&
                     resource.data.visibility == 'premium';

      // Allow full access for blog authors
      allow read, write: if isAuthenticated() &&
                            isOwner(resource.data.authorId);

      // Allow creation for authenticated users
      allow create: if isAuthenticated() &&
                       request.resource.data.authorId == request.auth.uid &&
                       request.resource.data.keys().hasAll(['title', 'authorId', 'createdAt']) &&
                       request.resource.data.title is string &&
                       request.resource.data.authorId is string &&
                       request.resource.data.createdAt is timestamp;

      // Family admins can override blog permissions for family blogs
      allow read, write: if resource.data.familyId != null &&
                            isFamilyAdmin(resource.data.familyId);
    }

    // ===========================================
    // TEMPORARY OPEN ACCESS FOR OTHER COLLECTIONS
    // ===========================================
    // This rule allows access to other collections until proper rules are implemented
    match /{document=**} {
      allow read, write: if request.time < timestamp.date(2025, 6, 26);
    }
  }
}