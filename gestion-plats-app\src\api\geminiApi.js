// Remplacez cette clé par votre propre clé API Gemini
const GEMINI_API_KEY = "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"
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"

// Suggestions de fallback si l'API ne fonctionne pas
const suggestionsFallback = [
  {
    nom: "Riz Sauté aux Légumes",
    description: "Un délicieux riz sauté avec des légumes frais, parfait pour un repas équilibré et savoureux en famille.",
    ingredients: ["Riz", "Carottes", "Petits pois", "Oignons", "Huile", "Sel", "Poivre"],
    tempsPreparation: "25 minutes",
    difficulte: "Facile"
  },
  {
    nom: "Poulet Grillé aux Herbes",
    description: "Poulet tendre grillé avec un mélange d'herbes aromatiques, accompagné de légumes de saison pour un repas complet.",
    ingredients: ["Poulet", "Thym", "Romarin", "Ail", "Huile d'olive", "Citron", "Sel"],
    tempsPreparation: "45 minutes",
    difficulte: "Moyen"
  },
  {
    nom: "Salade Méditerranéenne",
    description: "Salade fraîche et colorée avec des légumes méditerranéens, feta et olives, idéale pour un déjeuner léger.",
    ingredients: ["Tomates", "Concombre", "Feta", "Olives", "Huile d'olive", "Basilic"],
    tempsPreparation: "15 minutes",
    difficulte: "Facile"
  }
]

export const obtenirSuggestionPlat = async (description) => {
  // Vérifier si la clé API est configurée
  if (!GEMINI_API_KEY || GEMINI_API_KEY === "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") {
    console.warn("Clé API Gemini non configurée, utilisation du mode fallback")
    return genererSuggestionFallback(description)
  }

  const prompt = `En tant qu'expert culinaire, suggère-moi un plat basé sur cette description: "${description}".

Réponds UNIQUEMENT avec un objet JSON dans ce format exact:
{
  "nom": "Nom du plat suggéré",
  "description": "Description détaillée du plat (minimum 50 caractères)",
  "ingredients": ["ingrédient 1", "ingrédient 2", "ingrédient 3"],
  "tempsPreparation": "temps en minutes",
  "difficulte": "Facile/Moyen/Difficile"
}

Ne pas inclure de texte avant ou après le JSON. Assure-toi que la description fait au moins 50 caractères.`

  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      })
    })

    if (!response.ok) {
      console.warn(`Erreur API Gemini ${response.status}, utilisation du fallback`)
      return genererSuggestionFallback(description)
    }

    const data = await response.json()

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      console.warn("Réponse API invalide, utilisation du fallback")
      return genererSuggestionFallback(description)
    }

    const texteReponse = data.candidates[0].content.parts[0].text

    try {
      const suggestionJson = JSON.parse(texteReponse.trim())

      if (!suggestionJson.nom || !suggestionJson.description) {
        throw new Error('Format de réponse invalide')
      }

      if (suggestionJson.description.length < 10) {
        suggestionJson.description = `${suggestionJson.description}. Un plat délicieux et savoureux parfait pour toute la famille.`
      }

      return {
        success: true,
        data: suggestionJson
      }
    } catch (parseError) {
      console.warn("Erreur de parsing, utilisation du fallback")
      return genererSuggestionFallback(description)
    }

  } catch (error) {
    console.warn('Erreur API Gemini, utilisation du fallback:', error)
    return genererSuggestionFallback(description)
  }
}

// Fonction de fallback qui génère des suggestions basées sur la description
const genererSuggestionFallback = (description) => {
  const descriptionLower = description.toLowerCase()

  // Logique simple pour choisir une suggestion appropriée
  let suggestion = suggestionsFallback[0] // Par défaut

  if (descriptionLower.includes('poulet') || descriptionLower.includes('viande')) {
    suggestion = suggestionsFallback[1]
  } else if (descriptionLower.includes('salade') || descriptionLower.includes('léger') || descriptionLower.includes('végétarien')) {
    suggestion = suggestionsFallback[2]
  } else if (descriptionLower.includes('rapide') || descriptionLower.includes('facile')) {
    suggestion = suggestionsFallback[0]
  }

  // Personnaliser légèrement la suggestion
  const suggestionPersonnalisee = {
    ...suggestion,
    description: `${suggestion.description} Suggestion basée sur votre demande: "${description}".`
  }

  return {
    success: true,
    data: suggestionPersonnalisee
  }
}
