const OPENROUTER_API_KEY = "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"
const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"

export const obtenirSuggestionPlat = async (description) => {
  const prompt = `En tant qu'expert culinaire, suggère-moi un plat basé sur cette description: "${description}".

Réponds UNIQUEMENT avec un objet JSON dans ce format exact:
{
  "nom": "Nom du plat suggéré",
  "description": "Description détaillée du plat (minimum 50 caractères)",
  "ingredients": ["ingrédient 1", "ingrédient 2", "ingrédient 3"],
  "tempsPreparation": "temps en minutes",
  "difficulte": "Facile/Moyen/Difficile"
}

Ne pas inclure de texte avant ou apr<PERSON> le <PERSON>SON.`

  try {
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'http://localhost:5174',
        'X-Title': 'Planificateur Repas'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-r1-0528:free',
        messages: [
          {
            role: 'system',
            content: 'Tu es un chef cuisinier expert qui suggère des plats. Réponds toujours avec un JSON valide uniquement.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7
      })
    })

    if (!response.ok) {
      throw new Error(`Erreur API: ${response.status}`)
    }

    const data = await response.json()

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Réponse API invalide')
    }

    const texteReponse = data.choices[0].message.content.trim()

    try {
      const suggestionJson = JSON.parse(texteReponse)

      if (!suggestionJson.nom || !suggestionJson.description) {
        throw new Error('Format de réponse invalide')
      }

      if (suggestionJson.description.length < 10) {
        suggestionJson.description = `${suggestionJson.description}. Un plat délicieux et savoureux parfait pour toute la famille.`
      }

      return {
        success: true,
        data: suggestionJson
      }
    } catch (parseError) {
      return {
        success: false,
        error: 'Impossible de parser la réponse de l\'IA'
      }
    }

  } catch (error) {
    console.error('Erreur API:', error)
    return {
      success: false,
      error: error.message || 'Erreur de connexion à l\'API'
    }
  }
}
