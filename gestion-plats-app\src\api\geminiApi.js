const GEMINI_API_KEY = "AIzaSyBfXa4eLs5pab9Zia5kS8idvHx-tMssc94"
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"

export const obtenirSuggestionPlat = async (description) => {
  const prompt = `En tant qu'expert culinaire, suggère-moi un plat basé sur cette description: "${description}".

Réponds UNIQUEMENT avec un objet JSON dans ce format exact:
{
  "nom": "Nom du plat suggéré",
  "description": "Description détaillée du plat (minimum 50 caractères)",
  "ingredients": ["ingrédient 1", "ingrédient 2", "ingrédient 3"],
  "tempsPreparation": "temps en minutes",
  "difficulte": "Facile/Moyen/Difficile"
}

Ne pas inclure de texte avant ou après le JSON. Assure-toi que la description fait au moins 50 caractères.`

  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      })
    })

    if (!response.ok) {
      throw new Error(`Erreur API: ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Réponse API invalide')
    }

    const texteReponse = data.candidates[0].content.parts[0].text
    
    try {
      const suggestionJson = JSON.parse(texteReponse.trim())
      
      if (!suggestionJson.nom || !suggestionJson.description) {
        throw new Error('Format de réponse invalide')
      }
      
      if (suggestionJson.description.length < 10) {
        suggestionJson.description = `${suggestionJson.description}. Un plat délicieux et savoureux parfait pour toute la famille.`
      }
      
      return {
        success: true,
        data: suggestionJson
      }
    } catch (parseError) {
      return {
        success: false,
        error: 'Impossible de parser la réponse de l\'IA'
      }
    }
    
  } catch (error) {
    console.error('Erreur API Gemini:', error)
    return {
      success: false,
      error: error.message || 'Erreur de connexion à l\'API'
    }
  }
}
