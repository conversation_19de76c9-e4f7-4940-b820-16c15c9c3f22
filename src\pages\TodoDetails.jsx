import React, { use, useEffect, useState } from 'react'
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { getTodoById, getTodoHelp, updateTodo } from '../store/todoSlice';
import * as yup from "yup"
import { yupResolver } from '@hookform/resolvers/yup'
import { useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import ReactMarkdown from 'react-markdown';


const schema = yup
  .object({
    title: yup.string().max(100).required(),
    description: yup.string().max(500).optional(),
    completed: yup.boolean().default(false),
    projectId: yup.number().required(),
    createdAt: yup.date().default(() => new Date()),
    aiHelp: yup.string().optional(),
  })
  .required()
export default function TodoDetails() {
    const [isEditing, setIsEditing] = useState(false);
    const todoId=useParams().todoId;
    //const todo=useSelector(getTodoById(todoId));
    const todo=useSelector((state) => state.todo.todos.find((t) => t.id == todoId));
    const projects = useSelector((state) => state.project.projects);
    const project = useSelector((state) => state.project.projects.find((p) => p.id == todo?.projectId));
    const {
        register,
        handleSubmit,
        formState: { errors },
      } = useForm({
        resolver: yupResolver(schema),
      })
      const dispatch = useDispatch()

    
    // useEffect(() => {
    //     const todo=useSelector(getTodoById(todoId));
    // },[])
    
  return (
    <div className="min-h-screen bg-gray-100 p-8">
        <div className="bg-white p-6 rounded shadow-md mb-6 flex justify-between items-center">
            <h2 className="text-3xl font-bold mb-4">Todo Details</h2>
            <button 
            onClick={() => setIsEditing(true)} 
            className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded">
                Edit
                </button>
                <button
                    onClick={() => dispatch(getTodoHelp(todo, project))}
                    className="bg-gray-500 hover:bg-gray-600 text-white p-2 rounded"
                >
                    Ai Help
                </button>
        </div>

        {todo ? (isEditing ? (
            <div>
                <form onSubmit={handleSubmit((data) => {
                    dispatch(updateTodo({...data, id: todo.id}));
                    setIsEditing(false);
                })}>
                    
                    <div className="mb-4">
                        <label className="block text-gray-700 mb-2" htmlFor="title">Title</label>
                        <input
                            {...register("title")}
                            type="text"
                            id="title"
                            defaultValue={todo.title}
                            className={`w-full p-2 border ${errors.title ? 'border-red-500' : 'border-gray-300'} rounded`}
                        />
                        {errors.title && <p className="text-red-500 text-sm">{errors.title.message}</p>}
                    </div>
                    <div className="mb-4">
                        <label className="block text-gray-700 mb-2" htmlFor="description">Description</label>
                        <textarea
                            {...register("description")}
                            id="description"
                            rows={4}
                            defaultValue={todo.description || ""}
                            className={`w-full p-2 border ${errors.description ? 'border-red-500' : 'border-gray-300'} rounded`}
                        ></textarea>
                        {errors.description && <p className="text-red-500 text-sm">{errors.description.message}</p>}
                    </div>
                    <div className="mb-4">
                        <label className="block text-gray-700 mb-2" htmlFor="completed">Completed</label>
                        <select
                            {...register("completed")}
                            id="completed"
                            className={`w-full p-2 border ${errors.completed ? 'border-red-500' : 'border-gray-300'} rounded`}
                        >
                            <option value="true">Yes</option>
                            <option value="false">No</option>
                        </select>
                        {errors.completed && <p className="text-red-500 text-sm">{errors.completed.message}</p>}
                    </div>
                    <div className="mb-4">
                        <label className="block text-gray-700 mb-2" htmlFor="projectId">Project ID</label>
                        <select
                            {...register("projectId")}
                            id="projectId"
                            className={`w-full p-2 border ${errors.projectId ? 'border-red-500' : 'border-gray-300'} rounded`}
                        >
                            {projects.map((project) => (
                                <option key={project.id} value={project.id}>
                                    {project.name}
                                </option>
                            ))}
                        </select>
                        {errors.projectId && <p className="text-red-500 text-sm">{errors.projectId.message}</p>}
                    </div>
                    <div className="mb-4">
                        <label className="block text-gray-700 mb-2" htmlFor="aiHelp">AI Help</label>
                        <textarea
                            {...register("aiHelp")}
                            id="aiHelp"
                            rows={4}
                            defaultValue={todo.aiHelp || ""}
                            className={`w-full p-2 border ${errors.aiHelp ? 'border-red-500' : 'border-gray-300'} rounded`}
                        ></textarea>
                        {errors.aiHelp && <p className="text-red-500 text-sm">{errors.aiHelp.message}</p>}
                    </div>
                    <button
                        type="submit"
                        className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
                    >
                        Save
                    </button>
                </form>
            </div>
        ) : (
            <div className="bg-white p-6 rounded shadow-md">
                <h3 className="text-xl font-semibold mb-2">Title:{todo.title}</h3>
                <p className="text-gray-700 mb-2">Completed: {todo.completed ? "Yes" : "No"}</p>
                <p className="text-gray-500">Created At: {todo.createdAt}</p>
                <p className="text-gray-500">Project ID: {todo.projectId}</p>
                <p className="text-gray-500">Description: {todo.description || "No description provided"}</p>
                <p className="text-gray-500">
                    AI Help: <ReactMarkdown children={todo.aiHelp || "No help provided"} /></p>
            </div>
        )) : (
            <p className="text-red-500">Todo not found</p>
        )}
    </div>
  )
}
