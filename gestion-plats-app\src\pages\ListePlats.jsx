import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { getPlats, supprimerPlat } from '../store/platSlice'
import { getIngredients } from '../store/inventaireSlice'
import { verifierDisponibiliteIngredients, getIngredientsManquants } from '../utils/stockUtils'

export default function ListePlats() {
  const dispatch = useDispatch()
  const plats = useSelector(getPlats)
  const ingredients = useSelector(getIngredients)
  const [filtreCategorie, setFiltreCategorie] = useState('')
  const [recherche, setRecherche] = useState('')

  const categories = ['Entrée', 'Plat principal', 'Dessert', 'Boisson', 'Accompagnement']

  const platsFiltres = plats.filter(plat => {
    const correspondRecherche = plat.nom_plat.toLowerCase().includes(recherche.toLowerCase())
    const correspondCategorie = !filtreCategorie || plat.categorie === filtreCategorie
    return correspondRecherche && correspondCategorie
  })

  const supprimerPlatHandler = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce plat ?')) {
      dispatch(supprimerPlat(id))
    }
  }

  const getStatutDisponibilite = (plat) => {
    const disponible = verifierDisponibiliteIngredients(plat, ingredients)
    const ingredientsManquants = getIngredientsManquants(plat, ingredients)
    
    return {
      disponible,
      ingredientsManquants
    }
  }

  return (
    <div className="bg-white border-2 border-gray-400 mt-4">
      <div className="bg-gray-300 border-b-2 border-gray-400 px-4 py-2">
        <h2 className="text-lg font-bold text-gray-800">CONSULTATION DES PLATS ENREGISTRES</h2>
      </div>
      <div className="p-4">

        <table className="w-full border-2 border-gray-400 mb-4">
          <tr className="bg-gray-100">
            <td className="border border-gray-400 px-3 py-2 font-bold">RECHERCHE</td>
            <td className="border border-gray-400 px-3 py-2 font-bold">FILTRE CATEGORIE</td>
          </tr>
          <tr>
            <td className="border border-gray-400 px-3 py-2">
              <input
                type="text"
                value={recherche}
                onChange={(e) => setRecherche(e.target.value)}
                className="w-full p-2 border-2 border-gray-300"
                placeholder="Nom du plat..."
              />
            </td>
            <td className="border border-gray-400 px-3 py-2">
              <select
                value={filtreCategorie}
                onChange={(e) => setFiltreCategorie(e.target.value)}
                className="w-full p-2 border-2 border-gray-300"
              >
                <option value="">-- Toutes --</option>
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </td>
          </tr>
        </table>

        {platsFiltres.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 text-lg">
              {plats.length === 0 
                ? "Aucun plat enregistré. Commencez par ajouter un plat !" 
                : "Aucun plat ne correspond à vos critères de recherche."
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {platsFiltres.map(plat => {
              const { disponible, ingredientsManquants } = getStatutDisponibilite(plat)
              
              return (
                <div key={plat.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-gray-800">{plat.nom_plat}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      disponible 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {disponible ? 'Disponible' : 'Indisponible'}
                    </span>
                  </div>

                  <div className="mb-3">
                    <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                      {plat.categorie}
                    </span>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-medium text-gray-700 mb-2">Ingrédients:</h4>
                    <div className="space-y-1">
                      {plat.ingredients.map((ingredient, index) => (
                        <div key={index} className="text-sm text-gray-600 flex justify-between">
                          <span>{ingredient.nom}</span>
                          <span className="font-medium">{ingredient.quantite} {ingredient.unite}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {!disponible && ingredientsManquants.length > 0 && (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
                      <h5 className="font-medium text-red-800 mb-2">Ingrédients manquants:</h5>
                      <div className="space-y-1">
                        {ingredientsManquants.map((ingredient, index) => (
                          <div key={index} className="text-sm text-red-700">
                            <span className="font-medium">{ingredient.nom}</span>
                            {ingredient.quantite_disponible !== undefined ? (
                              <span className="ml-2">
                                (Requis: {ingredient.quantite_requise} {ingredient.unite}, 
                                Disponible: {ingredient.quantite_disponible} {ingredient.unite})
                              </span>
                            ) : (
                              <span className="ml-2">({ingredient.raison})</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center text-sm text-gray-500 mb-3">
                    <span>ID: {plat.id}</span>
                    <span>Créé: {plat.createdAt}</span>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={() => supprimerPlatHandler(plat.id)}
                      className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors text-sm"
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        )}

        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Statistiques</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-700 font-medium">Total plats:</span>
              <span className="ml-2 text-blue-900">{plats.length}</span>
            </div>
            <div>
              <span className="text-green-700 font-medium">Disponibles:</span>
              <span className="ml-2 text-green-900">
                {plats.filter(plat => verifierDisponibiliteIngredients(plat, ingredients)).length}
              </span>
            </div>
            <div>
              <span className="text-red-700 font-medium">Indisponibles:</span>
              <span className="ml-2 text-red-900">
                {plats.filter(plat => !verifierDisponibiliteIngredients(plat, ingredients)).length}
              </span>
            </div>
            <div>
              <span className="text-purple-700 font-medium">Catégories:</span>
              <span className="ml-2 text-purple-900">
                {new Set(plats.map(plat => plat.categorie)).size}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
