import React from 'react'
import { useForm } from 'react-hook-form'
import * as yup from "yup"
import { yupResolver } from '@hookform/resolvers/yup'
import { useDispatch } from 'react-redux'

import { addProject as addAction} from '../store/projectSlice'
const schema = yup
  .object({
    name: yup.string().max(100).required(),
    description: yup.string().required(),
  })
  .required()
export default function AddProject() {
    const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  })
  const dispatch = useDispatch()

  const addProject = (data) => {
    
    dispatch(addAction(data)) // Dispatch the action to add the project
    
    
  }
  return (
    <>
        <div className="min-h-screen bg-gray-100 p-8">
            <h1 className="text-3xl font-bold mb-4">Add New Project</h1>
            <form onSubmit={handleSubmit(addProject)} className="bg-white p-6 rounded shadow-md">
            <div className="mb-4">
                <label className="block text-gray-700 mb-2" htmlFor="name">Project Name</label>
                <input
                {...register("name")}
                type="text"
                id="name"
                className={`w-full p-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded`}
                />
                {errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
            </div>
            <div className="mb-4">
                <label className="block text-gray-700 mb-2" htmlFor="description">Description</label>
                <textarea
                {...register("description")}
                id="description"
                rows={4}
                className={`w-full p-2 border ${errors.description ? 'border-red-500' : 'border-gray-300'} rounded`}
                ></textarea>
                {errors.description && <p className="text-red-500 text-sm">{errors.description.message}</p>}
            </div>
            <button type="submit" className="bg-purple-500 hover:bg-purple-600 text-white p-2 rounded">
                Add Project
            </button>
            </form>
        </div>
    </>
  )
}


