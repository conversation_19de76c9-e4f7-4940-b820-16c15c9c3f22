import React, { useEffect } from "react";

import { useState } from "react";
import { get } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { Link, useSearchParams } from "react-router-dom";
import { getProjectById, getProjects } from "../store/projectSlice";
import { addTodo, getTodos, toggleTodo } from "../store/todoSlice";

export default function TodoList() {
  const [todoTitle, setTodoTitle] = useState("");
  const sParams = useSearchParams();
  const projectId = parseInt(sParams[0].get("projectId")) || 1; // Default to projectId 1 if not provided
  const allTodos = useSelector(getTodos);
  const [todos, setTodos] = useState(allTodos);
  const [project, setProject] = useState();
  const dispatch = useDispatch();
  const projectData = useSelector(getProjects);
  // useEffect(() => {
  //   console.log("Project ID:", projectId,!isNaN(projectId) && projectId > 1);
  //   if (!isNaN(projectId) && projectId >= 1) {
  //     setTodos(allTodos.filter((todo) => todo.projectId === projectId));
    
  //     const p = projectData.find((pro) => { 
  //       console.log("Project:", pro.id, projectId);
  //       return pro.id === projectId 
  //   });
  //     setProject(
  //       // @ts-ignore
  //       p
  //     );
  //   } else {
  //     setTodos(allTodos);
  //   }
  // }, [projectId,allTodos]);

  const handleInputChange = (e) => {
    setTodoTitle(e.target.value);
  };

  const createTodo = (title) => {
    const newTodo = {
      title,
      projectId,
    };
    dispatch(addTodo(newTodo));
  };
  const changeTodo = (id) => {
    dispatch(toggleTodo(id));
  };
  return (
    <>
      <div className="min-h-screen bg-gray-100 p-8">
        <h1 className="text-3xl font-bold mb-4">
            {project ? `Todos for ${project.
// @ts-ignore
            name}` : "Todo List"}
            </h1>
        <div className="mb-4">
          <input
            onChange={handleInputChange}
            value={todoTitle}
            type="text"
            placeholder="Add a new todo"
            className="p-2 border rounded w-full mb-4"
          />
          <button
            onClick={() => createTodo(todoTitle)}
            className="bg-blue-500 text-white p-2 rounded w-full"
          >
            Add Todo
          </button>
        </div>
        <div>
          <ul className="list-none p-0">
            {todos.map((todo) => (
              <li
                key={todo.id}
                className={`p-2 mb-2 rounded ${
                  todo.completed ? "bg-green-100" : "bg-white"
                } shadow`}
              >
                <div className="flex justify-between items-center">
                  <span className="text-lg">{todo.title}</span>
                  <button
                    onClick={() => changeTodo(todo.id)}
                    className={`p-2 rounded ${
                      todo.completed ? "bg-red-500" : "bg-green-500"
                    } text-white`}
                  >
                    {todo.completed ? "Undo" : "Complete"}
                  </button>
                </div>
                <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">
                  Created at: {todo.createdAt}
                </span>
                <span className="text-sm text-gray-500">
                  <Link
                    to={`/todos/${todo.id}`}
                    className="text-blue-500 hover:underline"
                  >
                    View Details
                  </Link>
                </span>
                <span className="text-sm text-gray-500">
                  <Link
                    to={`/project/${todo.projectId}`}
                    className="text-blue-500 hover:underline"
                  >
                    Project {todo.projectId}
                  </Link>
                </span>
                </div>
                
              </li>
            ))}
          </ul>
        </div>
      </div>
    </>
  );
}
