import { createSlice } from "@reduxjs/toolkit";

const platSlice = createSlice({
  name: "plats",
  initialState: {
    plats: [],
    loading: false,
    error: null,
  },
  reducers: {
    ajouterPlat: (state, action) => {
      const nouveauPlat = {
        id: Date.now(),
        nom_plat: action.payload.nom_plat,
        categorie: action.payload.categorie,
        ingredients: action.payload.ingredients,
        createdAt: new Date().toLocaleString(),
      };
      state.plats.push(nouveauPlat);
    },
    supprimerPlat: (state, action) => {
      state.plats = state.plats.filter(plat => plat.id !== action.payload);
    },
    modifierPlat: (state, action) => {
      const { id, ...updates } = action.payload;
      const plat = state.plats.find(p => p.id === id);
      if (plat) {
        Object.assign(plat, updates);
        plat.updatedAt = new Date().toLocaleString();
      }
    },
  },
});

// Selectors
export const getPlats = (state) => state.plats.plats;
export const getPlatById = (state, id) => state.plats.plats.find(plat => plat.id === id);
export const getPlatsByCategorie = (state, categorie) =>
  state.plats.plats.filter(plat => plat.categorie === categorie);

export const { ajouterPlat, supprimerPlat, modifierPlat } = platSlice.actions;
export default platSlice.reducer;
