import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { get } from "react-hook-form";
import { getAiHelp } from "../services/ai";

export const getTodoHelp = createAsyncThunk(
  "todo/getTodoHelp",
  async (todo,project,) => {
    
    try{
      const aiHelp = await getAiHelp(todo,project);
      return { ...todo, aiHelp };
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
);
const todoSlice = createSlice({
  name: "todo",
  initialState: {
    todos: [
        
      
      ],
    loading: false,
    error: null,
  },
  reducers: {
    addTodo: (state, action) => {
      state.todos.push({
        id: state.todos.length + 1,
        completed: false,
        projectId: action.payload.projectId,
        description: null,
        aiHelp: null,
        title: action.payload.title,
        createdAt: (new Date()).toLocaleString(),
      });
    },
    toggleTodo: (state, action) => {
      const todo = state.todos.find(todo => todo.id === action.payload);
      if (todo) {
        todo.completed = !todo.completed;
      }
    },
    updateTodo: (state, action) => {
      const { id, title, description, aiHelp, completed } = action.payload;
      const todo = state.todos.find(todo => todo.id === id);
      if (todo) {
        todo.title = title;
        todo.description = description;
        todo.aiHelp = aiHelp;
        todo.completed = completed;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getTodoHelp.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTodoHelp.fulfilled, (state, action) => {
        const todoIndex = state.todos.findIndex(todo => todo.id === action.payload.id);
        if (todoIndex !== -1) {
          state.todos[todoIndex] = action.payload;
        }
        state.loading = false;
      })
      .addCase(getTodoHelp.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      });
  },

  selectors: {
    getTodos: (state) => state.todos,
    getTodoById: (state, id) => state.todos.find(todo => todo.id === id),
  },
});

export const { getTodos, getTodoById } = todoSlice.selectors;
export const { addTodo, toggleTodo, updateTodo } = todoSlice.actions;
export default todoSlice.reducer;