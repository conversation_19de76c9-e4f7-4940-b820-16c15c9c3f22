import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { ajouterPlat } from '../store/platSlice'
import { getIngredients, decrementerStock } from '../store/inventaireSlice'
import { verifierDisponibiliteIngredients, envoyerEmailAlerte } from '../utils/stockUtils'

const schema = yup.object({
  nom_plat: yup.string().min(2, 'Nom trop court').max(100, 'Nom trop long').required('Nom du plat requis'),
  categorie: yup.string().required('Catégorie requise'),
}).required()

const categories = [
  'Entrée', 'Plat principal', 'Dessert', 'Boisson', 'Accompagnement'
]

const unites = ['g', 'kg', 'ml', 'L', 'pièces', 'cuillères']

export default function GestionPlats() {
  const dispatch = useDispatch()
  const ingredients = useSelector(getIngredients)
  const [ingredientsPlat, setIngredientsPlat] = useState([])
  const [nouvelIngredient, setNouvelIngredient] = useState({
    nom: '',
    quantite: '',
    unite: 'g'
  })
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    resolver: yupResolver(schema),
  })

  const ajouterIngredientAuPlat = () => {
    if (!nouvelIngredient.nom.trim() || !nouvelIngredient.quantite || nouvelIngredient.quantite <= 0) {
      afficherMessage('Veuillez remplir tous les champs de l\'ingrédient', 'error')
      return
    }

    const ingredientExiste = ingredientsPlat.find(ing => 
      ing.nom.toLowerCase() === nouvelIngredient.nom.toLowerCase()
    )

    if (ingredientExiste) {
      afficherMessage('Cet ingrédient est déjà ajouté au plat', 'error')
      return
    }

    const nouvelIngredientObj = {
      id: Date.now(),
      nom: nouvelIngredient.nom.trim(),
      quantite: parseFloat(nouvelIngredient.quantite),
      unite: nouvelIngredient.unite
    }

    setIngredientsPlat([...ingredientsPlat, nouvelIngredientObj])
    setNouvelIngredient({ nom: '', quantite: '', unite: 'g' })
    afficherMessage('Ingrédient ajouté au plat', 'success')
  }

  const supprimerIngredientDuPlat = (id) => {
    setIngredientsPlat(ingredientsPlat.filter(ing => ing.id !== id))
    afficherMessage('Ingrédient supprimé du plat', 'success')
  }

  const afficherMessage = (msg, type) => {
    setMessage(msg)
    setMessageType(type)
    setTimeout(() => {
      setMessage('')
      setMessageType('')
    }, 3000)
  }

  const creerPlat = (data) => {
    if (ingredientsPlat.length === 0) {
      afficherMessage('Veuillez ajouter au moins un ingrédient', 'error')
      return
    }

    const nouveauPlat = {
      nom_plat: data.nom_plat,
      categorie: data.categorie,
      ingredients: ingredientsPlat
    }

    const peutCreerPlat = verifierDisponibiliteIngredients(nouveauPlat, ingredients)

    if (!peutCreerPlat) {
      afficherMessage('Stock insuffisant pour créer ce plat', 'error')
      return
    }

    dispatch(ajouterPlat(nouveauPlat))

    ingredientsPlat.forEach(ingredientPlat => {
      dispatch(decrementerStock({
        nom_ingredient: ingredientPlat.nom,
        quantite: ingredientPlat.quantite
      }))

      const ingredientStock = ingredients.find(ing => 
        ing.nom_ingredient.toLowerCase() === ingredientPlat.nom.toLowerCase()
      )

      if (ingredientStock) {
        const nouvelleQuantite = ingredientStock.quantite_stock - ingredientPlat.quantite
        if (nouvelleQuantite < ingredientStock.seuil_alerte) {
          envoyerEmailAlerte(
            ingredientStock.nom_ingredient,
            nouvelleQuantite,
            ingredientStock.unite,
            ingredientStock.seuil_alerte
          )
        }
      }
    })

    reset()
    setIngredientsPlat([])
    afficherMessage('Plat créé avec succès !', 'success')
  }

  return (
    <div className="bg-white border-2 border-gray-400 mt-4">
      <div className="bg-gray-300 border-b-2 border-gray-400 px-4 py-2">
        <h2 className="text-lg font-bold text-gray-800">ENREGISTREMENT NOUVEAU PLAT</h2>
      </div>

      <div className="p-4">
        {message && (
          <div className={`border-2 p-3 mb-4 ${
            messageType === 'success'
              ? 'bg-green-50 text-green-800 border-green-400'
              : 'bg-red-50 text-red-800 border-red-400'
          }`}>
            <strong>{messageType === 'success' ? 'SUCCES:' : 'ERREUR:'}</strong> {message}
          </div>
        )}

        <form onSubmit={handleSubmit(creerPlat)}>
          <table className="w-full border-2 border-gray-400 mb-4">
            <tr className="bg-gray-100">
              <td className="border border-gray-400 px-3 py-2 font-bold">NOM DU PLAT *</td>
              <td className="border border-gray-400 px-3 py-2 font-bold">CATEGORIE *</td>
            </tr>
            <tr>
              <td className="border border-gray-400 px-3 py-2">
                <input
                  {...register("nom_plat")}
                  type="text"
                  className={`w-full p-2 border-2 ${
                    errors.nom_plat ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Ex: Riz au poulet"
                />
                {errors.nom_plat && (
                  <div className="text-red-600 text-sm mt-1 font-bold">ERREUR: {errors.nom_plat.message}</div>
                )}
              </td>
              <td className="border border-gray-400 px-3 py-2">
                <select
                  {...register("categorie")}
                  className={`w-full p-2 border-2 ${
                    errors.categorie ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">-- Choisir --</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {errors.categorie && (
                  <div className="text-red-600 text-sm mt-1 font-bold">ERREUR: {errors.categorie.message}</div>
                )}
              </td>
            </tr>
          </table>

          <div className="border-t-2 border-gray-400 pt-4">
            <h3 className="text-lg font-bold mb-3 text-gray-800 bg-gray-200 p-2 border-2 border-gray-400">
              INGREDIENTS DU PLAT
            </h3>

            <table className="w-full border-2 border-gray-400 mb-4">
              <tr className="bg-gray-100">
                <td className="border border-gray-400 px-3 py-2 font-bold">INGREDIENT</td>
                <td className="border border-gray-400 px-3 py-2 font-bold">QUANTITE</td>
                <td className="border border-gray-400 px-3 py-2 font-bold">UNITE</td>
                <td className="border border-gray-400 px-3 py-2 font-bold">ACTION</td>
              </tr>
              <tr>
                <td className="border border-gray-400 px-3 py-2">
                  <input
                    type="text"
                    value={nouvelIngredient.nom}
                    onChange={(e) => setNouvelIngredient({...nouvelIngredient, nom: e.target.value})}
                    className="w-full p-2 border-2 border-gray-300"
                    placeholder="Ex: Riz"
                  />
                </td>
                <td className="border border-gray-400 px-3 py-2">
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    value={nouvelIngredient.quantite}
                    onChange={(e) => setNouvelIngredient({...nouvelIngredient, quantite: e.target.value})}
                    className="w-full p-2 border-2 border-gray-300"
                    placeholder="500"
                  />
                </td>
                <td className="border border-gray-400 px-3 py-2">
                  <select
                    value={nouvelIngredient.unite}
                    onChange={(e) => setNouvelIngredient({...nouvelIngredient, unite: e.target.value})}
                    className="w-full p-2 border-2 border-gray-300"
                  >
                    {unites.map(unite => (
                      <option key={unite} value={unite}>{unite}</option>
                    ))}
                  </select>
                </td>
                <td className="border border-gray-400 px-3 py-2 text-center">
                  <button
                    type="button"
                    onClick={ajouterIngredientAuPlat}
                    className="bg-green-700 text-white px-4 py-2 border-2 border-green-800 font-bold"
                  >
                    AJOUTER
                  </button>
                </td>
              </tr>
            </table>

            {ingredientsPlat.length > 0 && (
              <div className="border-2 border-gray-400 bg-gray-50">
                <div className="bg-gray-200 border-b-2 border-gray-400 px-3 py-2">
                  <h4 className="font-bold text-gray-800">INGREDIENTS AJOUTES ({ingredientsPlat.length})</h4>
                </div>
                <div className="p-3">
                  <table className="w-full border border-gray-400">
                    <tr className="bg-gray-100">
                      <td className="border border-gray-400 px-2 py-1 font-bold">INGREDIENT</td>
                      <td className="border border-gray-400 px-2 py-1 font-bold">QUANTITE</td>
                      <td className="border border-gray-400 px-2 py-1 font-bold">UNITE</td>
                      <td className="border border-gray-400 px-2 py-1 font-bold">ACTION</td>
                    </tr>
                    {ingredientsPlat.map(ingredient => (
                      <tr key={ingredient.id}>
                        <td className="border border-gray-400 px-2 py-1">{ingredient.nom}</td>
                        <td className="border border-gray-400 px-2 py-1">{ingredient.quantite}</td>
                        <td className="border border-gray-400 px-2 py-1">{ingredient.unite}</td>
                        <td className="border border-gray-400 px-2 py-1 text-center">
                          <button
                            type="button"
                            onClick={() => supprimerIngredientDuPlat(ingredient.id)}
                            className="bg-red-700 text-white px-2 py-1 border border-red-800 text-xs font-bold"
                          >
                            SUPPR
                          </button>
                        </td>
                      </tr>
                    ))}
                  </table>
                </div>
              </div>
            )}
          </div>

          <div className="text-center mt-4 border-t-2 border-gray-400 pt-4">
            <button
              type="submit"
              className="bg-blue-800 text-white px-8 py-3 border-2 border-blue-900 font-bold text-lg"
            >
              ENREGISTRER LE PLAT
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
